#!/usr/bin/bash

# Environment variables to fix Triton f16 compilation issues on CUDA 7.5 (Turing)
export VLLM_USE_TRITON_FLASH_ATTN=0  # Disable Triton flash attention
export VLLM_ATTENTION_BACKEND=XFORMERS  # Use XFormers backend instead
export VLLM_DISABLED_KERNELS=""  # Clear any disabled kernels

# Additional Triton compilation fixes for Turing architecture
# export TRITON_INTERPRET=1  # Force Triton interpretation mode to avoid LLVM issues
# export TRITON_CACHE_DIR=/tmp/triton_cache  # Use custom cache directory
# export VLLM_TRITON_CACHE_MANAGER=0  # Disable Triton cache manager

# Force disable problematic Triton kernels for CUDA 7.5
export VLLM_DISABLE_TRITON_KERNELS=1  # Disable all Triton kernels
export VLLM_FORCE_EAGER_MODE=1  # Force eager execution mode

# Additional memory optimization environment variables
# export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:128"  # Reduce memory fragmentation
# export CUDA_VISIBLE_DEVICES=0,1,2,3  # Use only first 4 GPUs for vLLM

echo "Starting vLLM with optimized memory settings for expert parallelism..."
echo "GPU Memory Status before starting:"
nvidia-smi --query-gpu=index,memory.used,memory.total --format=csv,noheader,nounits | head -4

vllm serve \
    /models/gpustack_cache/model_scope/billy800/Qwen3-30B-A3B-Instruct-2507-AWQ/ \
    --max-model-len 32800 \
    --swap-space=8 \
    --tensor-parallel-size=4 \
    --dtype=float16 \
    --trust-remote-code \
    --quantization=awq \
    --host 0.0.0.0 \
    --gpu-memory-utilization=0.6 \
    --served-model-name qwen3-30b 2>&1 | tee vllm_error.log
    # --no-enable-chunked-prefill
    
    # --enable-chunked-prefill
    
    # Alternative models for testing:
    # /models/gpustack_cache/model_scope/billy800/Qwen3-30B-A3B-Instruct-2507-AWQ/ \
    # /models/gpustack_cache/model_scope/tclf90/Qwen3-235B-A22B-Thinking-2507-AWQ/ \
    # --enable-expert-parallel \