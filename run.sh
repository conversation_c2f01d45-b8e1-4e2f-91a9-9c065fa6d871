#!/usr/bin/bash

# Environment variables to fix Triton f16 compilation issues on CUDA 7.5
export VLLM_USE_TRITON_FLASH_ATTN=0  # Disable Triton flash attention
export VLLM_ATTENTION_BACKEND=XFORMERS  # Use XFormers backend instead
export VLLM_DISABLED_KERNELS=""  # Clear any disabled kernels

# Additional memory optimization environment variables
export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:128"  # Reduce memory fragmentation
export CUDA_VISIBLE_DEVICES=0,1,2,3  # Use only first 4 GPUs for vLLM

echo "Starting vLLM with optimized memory settings for expert parallelism..."
echo "GPU Memory Status before starting:"
nvidia-smi --query-gpu=index,memory.used,memory.total --format=csv,noheader,nounits | head -4

vllm serve \
    /models/gpustack_cache/model_scope/billy800/Qwen3-30B-A3B-Instruct-2507-AWQ/ \
    --max-model-len 32800 \
    --swap-space=8 \
    --tensor-parallel-size=4 \
    --dtype=float16 \
    --trust-remote-code \
    --disable-log-requests \
    --quantization=awq \
    --host 0.0.0.0 \
    --gpu-memory-utilization=0.75 \
    --served-model-name qwen3-30b \
    --no-enable-chunked-prefill

    # Alternative models for testing:
    # /models/gpustack_cache/model_scope/billy800/Qwen3-30B-A3B-Instruct-2507-AWQ/ \
    # /models/gpustack_cache/model_scope/tclf90/Qwen3-235B-A22B-Thinking-2507-AWQ/ \
    # --enable-expert-parallel \