#!/usr/bin/bash
SRC_DIR=~/Workspace/vllm
DST_DIR=~/miniconda3/envs/ktransformers/lib/python3.11/site-packages

REL_DIR=vllm/model_executor/layers/quantization
cp $SRC_DIR/$REL_DIR/moe_wna16.py $DST_DIR/$REL_DIR/
cp $SRC_DIR/$REL_DIR/awq.py $DST_DIR/$REL_DIR/

REL_DIR=vllm/model_executor/layers/fused_moe
cp $SRC_DIR/$REL_DIR/fused_moe.py $DST_DIR/$REL_DIR/
cp $SRC_DIR/$REL_DIR/configs/E=128,N=192,device_name=NVIDIA_GeForce_RTX_2080_Ti,dtype=int4_w4a16.json $DST_DIR/$REL_DIR/configs/


REL_DIR=vllm/attention/ops
cp $SRC_DIR/$REL_DIR/prefix_prefill.py $DST_DIR/$REL_DIR/
